import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = Router();

// GET /api/test - Test endpoint cơ bản
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Test endpoint accessed', { requestId: req.id });
  
  res.json({
    success: true,
    message: 'API is working!',
    data: {
      timestamp: new Date().toISOString(),
      requestId: req.id,
      method: req.method,
      path: req.path
    }
  });
}));

// GET /api/test/health - Health check chi tiết
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  };

  logger.info('Health check accessed', { requestId: req.id, healthData });

  res.json({
    success: true,
    message: 'System is healthy',
    data: healthData
  });
}));

// POST /api/test/echo - Echo endpoint để test POST request
router.post('/echo', asyncHandler(async (req: Request, res: Response) => {
  const { message, data } = req.body;

  logger.info('Echo endpoint accessed', { 
    requestId: req.id, 
    body: req.body 
  });

  res.json({
    success: true,
    message: 'Echo response',
    data: {
      received: {
        message,
        data,
        timestamp: new Date().toISOString()
      },
      requestInfo: {
        method: req.method,
        path: req.path,
        requestId: req.id,
        headers: {
          'content-type': req.get('content-type'),
          'user-agent': req.get('user-agent')
        }
      }
    }
  });
}));

// GET /api/test/error - Test error handling
router.get('/error', asyncHandler(async (req: Request, res: Response) => {
  const { type } = req.query;

  logger.info('Error test endpoint accessed', { 
    requestId: req.id, 
    errorType: type 
  });

  switch (type) {
    case 'validation':
      throw new AppError('Validation error example', 400, 'VALIDATION_ERROR', {
        field: 'email',
        message: 'Email is required'
      });
    
    case 'unauthorized':
      throw new AppError('Unauthorized access', 401, 'UNAUTHORIZED');
    
    case 'forbidden':
      throw new AppError('Access forbidden', 403, 'FORBIDDEN');
    
    case 'notfound':
      throw new AppError('Resource not found', 404, 'NOT_FOUND');
    
    default:
      throw new AppError('Internal server error example', 500, 'INTERNAL_ERROR');
  }
}));

// GET /api/test/async-error - Test async error handling
router.get('/async-error', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Async error test endpoint accessed', { requestId: req.id });

  // Simulate async operation that fails
  await new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new AppError('Async operation failed', 500, 'ASYNC_ERROR'));
    }, 100);
  });
}));

// GET /api/test/info - API information
router.get('/info', asyncHandler(async (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'API Information',
    data: {
      name: 'Zalo Chatbot API',
      version: '1.0.0',
      description: 'API for Zalo Chatbot with TypeScript',
      endpoints: {
        'GET /api/test': 'Basic test endpoint',
        'GET /api/test/health': 'Detailed health check',
        'POST /api/test/echo': 'Echo POST data',
        'GET /api/test/error?type=<type>': 'Test error handling (types: validation, unauthorized, forbidden, notfound)',
        'GET /api/test/async-error': 'Test async error handling',
        'GET /api/test/info': 'This endpoint'
      },
      timestamp: new Date().toISOString(),
      requestId: req.id
    }
  });
}));

export default router;
